import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, List } from "antd";
import { Toast } from "antd-mobile";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import filter from "@/Resources/icon/collapse.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import filterDark from "@/Resources/icon/collapse_dark.png";
import cameraIcon from "@/Resources/icon/camera-icon.png";
import successCof from "@/Resources/icon/success-cof.png";
import searchFail from "@/Resources/icon/search-fail.png";
import cameraIconDark from "@/Resources/icon/camera-icon-dark.png";
import successCofDark from "@/Resources/icon/success-cof-dark.png";
import searchFailDark from "@/Resources/icon/search-fail-dark.png";
import { useTheme } from "@/utils/themeDetector";
import PopoverSelector from "@/components/PopoverSelector";
import {
  getRecordingOptions,
  useRecordingSettings,
} from "@/pages/IPC/IPC_APP/AddDevice/LocalRecord/recordingSettings";
import { setupRecordCamera } from "@/api/ipc";
import { useRequest } from "ahooks";
import StatusDialog, { StatusType } from "@/components/StatusDialog";
import { useHistory } from "react-router-dom";
import { InfoCameraAI } from "@/pages/IPC/IPC_APP";

interface LocalRecordProps {
  visible?: boolean;
  onCancel?: () => void;
  onNext?: (configStatus: "configuring" | "success" | "error") => void;
  selectedDevices: any[]; // 设备列表由父组件传递
}

type ConfigStatus = "settings" | "configuring" | "success" | "error";

// 将配置状态映射到状态类型
const mapConfigStatusToStatus = (configStatus: ConfigStatus): StatusType => {
  if (configStatus === "configuring") return "loading";
  if (configStatus === "success") return "success";
  if (configStatus === "error") return "error";
  return "loading";
};

const CameraLocalRecord: React.FC<LocalRecordProps> = ({
  visible = true,
  onCancel,
  onNext,
  selectedDevices = [],
}) => {
  const { isDarkMode } = useTheme();
  const history = useHistory();

  // 添加状态管理，初始状态为设置页面
  const [configStatus, setConfigStatus] = useState<ConfigStatus>("settings");

  // 复用App端的录制设置逻辑，指定为App模式（保证quality有默认值）
  const {
    settings,
    popoverState,
    updatePopoverVisibility,
    handleDurationChange,
    handleModeChange,
    // handleQualityChange,
    calculateEstimatedSpace,
  } = useRecordingSettings(true);

  const { isHide } = InfoCameraAI();

  // 获取选项数据
  const { durationOptions, modeOptions, 
    // qualityOptions
   } =
    getRecordingOptions();

  useEffect(() => {
    calculateEstimatedSpace();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 设置API请求
  const { run: runSetupCamera } = useRequest(setupRecordCamera, {
    manual: true,
    debounceWait: 300,
    onSuccess: (result) => {
      console.log("摄像机录制设置成功:", result);
      setConfigStatus("success");
    },
    onError: (error) => {
      console.error("摄像机录制设置失败:", error);
      Toast.show({
        content: "设置失败，请重试",
        position: "bottom",
      });
      setConfigStatus("error");
    },
  });

  // 将时长转换为天数
  const getDurationDays = (durationText: string): number => {
    const days = parseInt(durationText.replace("天", ""), 10);
    return isNaN(days) ? 30 : days; // 默认30天
  };

  // 提取存储大小数字
  // const getStorageSizeGB = (sizeText: string): number => {
  //   const size = parseFloat(sizeText.replace("GB", ""));
  //   return isNaN(size) ? 50 : size; // 默认50GB
  // };

  const handleGoBack = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const handleNext = () => {
    // 获取选中设备的ID数组
    const cameraIds = selectedDevices.map((device: any) => device.id);
    if (cameraIds.length === 0) {
      Toast.show({
        content: "未选择任何设备",
        position: "center",
      });
      return;
    }
    // 构建camera_info参数
    const cameraInfo: { [key: string]: { model: string } } = {};
    selectedDevices.forEach((device: any) => {
      cameraInfo[device.id] = {
        model: device.model,
      };
    });

    // 构建请求参数
    const params = {
      camera: cameraIds,
      camera_info: cameraInfo,
      config: {
        path: settings.storagePath,
        retention_period: getDurationDays(settings.duration),
        record_mode: settings.mode === "连续录制" ? "continuous" : "event",
        // record_resolution: settings.quality,
        // space_limit: getStorageSizeGB(settings.estimatedSpace),
      },
    };

    console.log("调用摄像机录制设置API，参数:", params);

    // 切换到配置中状态
    setConfigStatus("configuring");
    runSetupCamera(params, { showLoading: false });
  };

  // 处理状态对话框的关闭
  const handleStatusClose = () => {
    setConfigStatus("settings");
  };

  // 处理成功状态下的"去查看"按钮
  const handleGotoManagement = async () => {
    // 调用onNext回调，通知父组件配置成功，并等待数据更新完成
    if (onNext) {
      await onNext("success");
    }
    // 数据更新完成后再跳转页面和关闭弹窗
    history.push("/cameraManagement_pc");
    // 关闭当前弹窗
    if (onCancel) {
      onCancel();
    }
  };

  const handleRetry = () => {
    setConfigStatus("settings");
  };

  const CustomArrow = () => (
    <img
      alt=""
      src={isDarkMode ? filterDark : filter}
      className={styles.customArrow}
    />
  );

  // 模态框标题组件
  const renderTitle = () => (
    <div className={styles.modalHeader}>
      <img
        alt=""
        src={isDarkMode ? arrowLeftDark : arrowLeft}
        className={styles.backIcon}
        onClick={handleGoBack}
      />
      <div className={styles.title}>摄像机本地录制</div>
    </div>
  );

  // 获取当前状态的配置
  const getStatusConfig = () => {
    switch (configStatus) {
      case "configuring":
        return {
          icon: isDarkMode ? cameraIconDark : cameraIcon,
          title: "摄像机配置中，请稍等",
          subTitle: "大概需要花费10秒",
          iconClassName: "circleIcon",
          closeIcon: isDarkMode ? arrowLeftDark : arrowLeft,
        };
      case "success":
        return {
          icon: isDarkMode ? successCofDark : successCof,
          title: "配置完成",
          closeIcon: isDarkMode ? arrowLeftDark : arrowLeft,
        };
      case "error":
        return {
          icon: isDarkMode ? searchFailDark : searchFail,
          title: "配置失败，请重试",
          closeIcon: isDarkMode ? arrowLeftDark : arrowLeft,
        };
      default:
        return {
          icon: isDarkMode ? cameraIconDark : cameraIcon,
          title: "配置中",
        };
    }
  };

  // 获取按钮配置
  const getButtonConfig = () => {
    switch (configStatus) {
      case "configuring":
        return undefined;
      case "success":
        return { text: "去看看", onClick: handleGotoManagement };
      case "error":
        return { text: "重试", onClick: handleRetry };
      default:
        return undefined;
    }
  };

  // 如果是配置相关状态，显示状态对话框
  if (configStatus !== "settings") {
    return (
      <StatusDialog
        visible={visible}
        onClose={handleStatusClose}
        statusType={mapConfigStatusToStatus(configStatus)}
        statusConfig={getStatusConfig()}
        buttonConfig={getButtonConfig()}
        modalTitle="配置中"
        closeIcon={isDarkMode ? arrowLeftDark : arrowLeft}
      />
    );
  }

  // 否则显示设置表单
  return (
    <Modal
      title={renderTitle()}
      open={visible}
      onCancel={handleGoBack}
      width={546}
      footer={
        <Button
          type="primary"
          className={styles.nextButton}
          onClick={handleNext}
          // loading={submitLoading}
        >
          下一步
        </Button>
      }
      centered
      maskClosable={false}
      className={styles.recordingModal}
      closeIcon={null}
      styles={{
        body: {
          height: "490px",
          overflow: "auto",
          display: "flex",
          flexDirection: "column",
          padding: "0",
        },
      }}
      style={{ height: "636px" }}
    >
      <div className={styles.modalContent}>
        <div className={styles.settingsSection}>
          <div className={styles.settingLabel}>视频存储设置</div>

          <List className={styles.settingsList}>
            <List.Item
              extra={<div className={styles.value}>{settings.storagePath}</div>}
              onClick={() => console.log("点击了存储目录")}
            >
              <div>存储目录</div>
            </List.Item>

            <PopoverSelector
              visible={popoverState.durationPopoverVisible}
              onVisibleChange={(visible) =>
                updatePopoverVisibility("durationPopoverVisible", visible)
              }
              value={settings.duration}
              options={durationOptions}
              onChange={handleDurationChange}
            >
              <List.Item
                extra={
                  <div className={styles.valueWithArrow}>
                    <div className={styles.value}>{settings.duration}</div>
                    <CustomArrow />
                  </div>
                }
                onClick={() =>
                  updatePopoverVisibility("durationPopoverVisible", true)
                }
              >
                <div>录像保存时长</div>
              </List.Item>
            </PopoverSelector>

            {
              !isHide &&(
                <PopoverSelector
                  visible={popoverState.modePopoverVisible}
                  onVisibleChange={(visible) =>
                    updatePopoverVisibility("modePopoverVisible", visible)
                  }
                  value={settings.mode}
                  options={modeOptions}
                  onChange={handleModeChange}
                >
                  <List.Item
                    extra={
                      <div className={styles.valueWithArrow}>
                        <div className={styles.value}>{settings.mode}</div>
                        <CustomArrow />
                      </div>
                    }
                    onClick={() =>
                      updatePopoverVisibility("modePopoverVisible", true)
                    }
                  >
                <div>录制模式</div>
              </List.Item>
                </PopoverSelector>
              )
            }

            {/* 录制清晰度（与App端一致，默认值和选中逻辑） */}
            {/* {qualityOptions && (
              <PopoverSelector
                visible={!!popoverState.qualityPopoverVisible}
                onVisibleChange={(visible) =>
                  updatePopoverVisibility("qualityPopoverVisible", visible)
                }
                value={settings.quality || ""}
                options={qualityOptions}
                onChange={handleQualityChange}
              >
                <List.Item
                  extra={
                    <div className={styles.valueWithArrow}>
                      <div className={styles.value}>{settings.quality}</div>
                      <CustomArrow />
                    </div>
                  }
                  onClick={() =>
                    updatePopoverVisibility("qualityPopoverVisible", true)
                  }
                >
                  <div>录制清晰度</div>
                </List.Item>
              </PopoverSelector>
            )} */}

            {/* {settings.mode === "连续录制" && (
              <List.Item
                extra={
                  <div className={styles.value}>{settings.estimatedSpace}</div>
                }
              >
                <div>预计占用存储空间</div>
              </List.Item>
            )} */}
          </List>
        </div>

        <div className={styles.hintText}>
          Xiaomi智能存储将为每个摄像头建立单独文件夹，更多设置可前往Xiaomi智能存储设备管理页完成。
        </div>
      </div>
    </Modal>
  );
};

export default CameraLocalRecord;
