import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { useHistory } from "react-router-dom";
import styles from "./index.module.scss";
import LocalRecord from "./LocalRecord";
import searching from "@/Resources/icon/searching.png";
import success from "@/Resources/icon/success.png";
import close from "@/Resources/icon/close.png";
import outline from "@/Resources/icon/outline.png";
import searchFail from "@/Resources/icon/search-fail.png";
import searchingDark from "@/Resources/icon/searching-dark.png";
import successDark from "@/Resources/icon/success-dark.png";
import closeDark from "@/Resources/icon/close_white.png";
import outlineDark from "@/Resources/icon/outline-dark.png";
import searchFailDark from "@/Resources/icon/search-fail-dark.png";
import { useTheme } from "@/utils/themeDetector";
import StatusDialog from "@/components/StatusDialog";
import SupportInformation from "../SupportInformation";
import { useDeviceManagement } from "@/pages/IPC/IPC_APP/AddDevice/deviceManagement";
import { listRecordCamera } from "@/api/ipc";
import { useRequest } from 'ahooks';
import { ICameraDetail } from "../../IPC_APP/CameraDetail";
import { cameraIconInfo } from "@/components/CameraPlayer/constants";
import { useCameras } from "..";
import { Toast } from "@/components/Toast/manager";
import { PreloadImage } from "@/components/Image";
interface AddDeviceProps {
  visible?: boolean;
  onClose?: () => void;
}

export default function AddDevice({ visible = true, onClose }: AddDeviceProps) {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const [localRecordVisible, setLocalRecordVisible] = useState(false);
  const [, setConfigurationVisible] = useState(false);
  const [, setConfigStatus] = useState<
    "configuring" | "success" | "error"
  >("configuring");
  const [supportInfoVisible, setSupportInfoVisible] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showEmptyDialog, setShowEmptyDialog] = useState(false);
  const cameraObj = useCameras(); // 已录制摄像机操作对象

  const { runAsync } = useRequest(listRecordCamera, {
    manual: true,
    defaultParams: [{ did: [] }],
    onError: (e) => {
      console.log('error', e);
      Toast.show('应用未授权应用获取IPC信息');
    },
    onSuccess: (data) => {
      if (data.code === 0 && data.data.camera) {
        const cameraList = data.data.camera.map((it: ICameraDetail) => {
          return { ...it, label: it.name, key: it.did, name: it.name, icon: cameraIconInfo(it.model) }
        })
        cameraObj.setCameras(cameraList);
      }

      if (data.code === 1700) {
        Toast.show('应用未授权应用获取IPC信息');
      }
    }
  })

  // 使用共用hook
  const {
    fetchState,
    toggleSelect,
    toggleSelectAll,
    handleRetry,
    selectedCount,
    selectedDevices,
  } = useDeviceManagement();

  // 页面曝光埋点
  useEffect(() => {
    if (visible) {
      window.onetrack?.('track', 'ipc_addDevice_expose');
    }
  }, [visible]);

  // 渲染查找状态（只处理 loading 和 success 状态）
  const renderContent = () => {
    switch (fetchState.status) {
      case "loading":
        return (
          <div className={styles.loadingContainer}>
            <div className={styles.searchingText}>
              <img
                alt=""
                src={isDarkMode ? searchingDark : searching}
                className={styles.blueIndicator}
              ></img>
              <span>查找中</span>
            </div>
          </div>
        );

      case "success":
        return (
          <div className={styles.deviceListContainer}>
            <div className={styles.searchResult}>
              <img src={isDarkMode ? successDark : success} alt="" style={{ width: '24px', height: '24px' }} />
              <span>找到{fetchState.devices.length}个米系摄像机设备</span>
            </div>
            <div className={styles.deviceList}>
              {fetchState.devices.map((device) => {
               return(
                 <div
                  key={device.id}
                  className={styles.deviceItem}
                  onClick={() => toggleSelect(device.id)}
                >
                  <div className={styles.deviceInfo}>
                    <div className={styles.deviceThumb}>
                      <PreloadImage
                        src={device.thumbnail}
                        alt={device.model}
                        style={{
                          width: 24,
                          height: 24,
                          objectFit: 'contain'
                        }}
                      />
                    </div>
                    <span className={styles.deviceName}>{device.name}</span>
                  </div>
                  <div className={styles.checkboxContainer}>
                    <div
                      className={`${styles.customCheckbox} ${device.selected ? styles.checked : ""
                        }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSelect(device.id);
                      }}
                    >
                      {device.selected && (
                        <div className={styles.checkIcon}></div>
                      )}
                    </div>
                  </div>
                </div>
               )
              })}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // 自定义模态框标题部分
  const customTitle = (
    <div className={styles.modalHeader}>
      <img
        src={isDarkMode ? closeDark : close}
        alt="关闭"
        className={styles.closeButton}
        onClick={() => {
          if (onClose) {
            onClose();
          } else {
            history.goBack();
          }
        }}
      />
      <div className={styles.modalTitle}>选择添加设备</div>
      {(fetchState.status === "loading" || fetchState.status === "success") && (
        <img
          alt=""
          className={styles.outline}
          src={isDarkMode ? outlineDark : outline}
          style={{
            opacity: fetchState.status === "loading" ? 0.5 : 1,
            cursor: fetchState.status === "success" ? "pointer" : "default",
          }}
          onClick={
            fetchState.status === "success" ? toggleSelectAll : undefined
          }
        />
      )}
    </div>
  );

  // 仅显示主对话框而不是状态对话框的情况
  const shouldShowMainModal =
    fetchState.status === "loading" || fetchState.status === "success";

  return (
    <>
      {/* 主对话框，仅在加载中或成功状态显示 */}
      {shouldShowMainModal && (
        <Modal
          title={customTitle}
          open={visible}
          onCancel={() => {
            if (onClose) {
              onClose();
            } else {
              history.goBack();
            }
          }}
          footer={
            fetchState.status === "success" ? (
              <Button
                type="primary"
                onClick={() => {
                  setLocalRecordVisible(true);
                }}
                disabled={selectedCount === 0}
                className={styles.nextButton}
              >
                下一步
              </Button>
            ) : (
              <div style={{ height: 44 }}></div>
            )
          }
          width={546}
          centered
          maskClosable={false}
          className={styles.addDeviceModal}
          closeIcon={null}
          styles={{
            body: {
              height: "430px",
              padding: 0,
            },
          }}
        >
          {renderContent()}
        </Modal>
      )}

      {/* 错误状态对话框 - 和主对话框互斥 */}
      {!shouldShowMainModal && (
        <StatusDialog
          visible={
            (showErrorDialog || fetchState.status === "error") && visible
          }
          onClose={() => {
            setShowErrorDialog(false);
            // 关闭所有弹窗
            onClose?.();
          }}
          statusType="error"
          statusConfig={{
            icon: isDarkMode ? searchFailDark : searchFail,
            title: "查找超时，请重试",
            iconClassName: "errorIcon",
          }}
          buttonConfig={{
            text: "重试",
            onClick: handleRetry,
          }}
          modalTitle="选择添加设备"
          closeIcon={isDarkMode ? closeDark : close}
        />
      )}

      {/* 空状态对话框 - 和主对话框互斥 */}
      {!shouldShowMainModal && (
        <StatusDialog
          visible={
            (showEmptyDialog || fetchState.status === "empty") && visible
          }
          onClose={() => {
            setShowEmptyDialog(false);
            // 关闭所有弹窗
            onClose?.();
          }}
          statusType="empty"
          statusConfig={{
            icon: isDarkMode ? searchFailDark : searchFail,
            title: "未找到米系摄像机",
            iconClassName: "errorIcon",
          }}
          buttonConfig={{
            text: "查看支持的摄像机",
            onClick: () => {
              setShowEmptyDialog(false);
              setSupportInfoVisible(true);
            },
          }}
          modalTitle="选择添加设备"
          closeIcon={isDarkMode ? closeDark : close}
        />
      )}

      {/* 本地录像配置弹窗 */}
      {localRecordVisible && (
        <LocalRecord
          visible={localRecordVisible}
          onCancel={() => {
            setLocalRecordVisible(false);
            // 清除可能存在的配置状态
            sessionStorage.removeItem("cameraConfigStatus");
          }}
          onNext={async (status) => {
            // 如果是配置中状态，保持本地录像窗口关闭，显示配置窗口
            if (status === "configuring") {
              setLocalRecordVisible(false);
              setConfigStatus("configuring");
              setConfigurationVisible(true);
            }
            // 如果是成功状态，先执行接口调用更新数据，然后关闭所有弹窗
            else if (status === "success") {
              setLocalRecordVisible(false);
              // 先执行接口调用更新摄像机数据
              await runAsync({ did: [] }).catch((e) => console.log(e));
              // 数据更新完成后关闭所有弹窗
              if (onClose) {
                onClose();
              }
            }
            // 如果是失败状态，显示配置窗口
            else if (status === "error") {
              setLocalRecordVisible(false);
              setConfigStatus(status);
              setConfigurationVisible(true);
            }
          }}
          selectedDevices={selectedDevices}
        />
      )}

      {/* 支持的摄像机弹窗 */}
      {supportInfoVisible && (
        <SupportInformation
          visible={supportInfoVisible}
          onClose={() => setSupportInfoVisible(false)}
        />
      )}
    </>
  );
}
