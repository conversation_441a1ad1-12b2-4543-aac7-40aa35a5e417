import folderIcon from "@/Resources/icon/file-icon.png";
import pdfIcon from "@/Resources/icon/pdfIcon.png";
import zipIcon from "@/Resources/icon/zipIcon.png";
import pptIcon from "@/Resources/icon/pptIcon.png";
import wordIcon from "@/Resources/icon/wordIcon.png";
import xlsIcon from "@/Resources/icon/xlsIcon.png";
import textIcon from "@/Resources/icon/textIcon.png";
import imageIcon from "@/Resources/icon/imgIcon.png";
import videoIcon from "@/Resources/icon/videoIcon.png";
import audioIcon from "@/Resources/icon/musicIcon.png";
// TaskManager专用图标
import btIcon from "@/Resources/icon/btIcon.png";
import otherIcon from "@/Resources/icon/otherIcon.png";
import apkIcon from "@/Resources/icon/apk.png";

// 文件类型定义
export const FileTypes = {
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
  WORD: 'word',
  EXCEL: 'excel',
  PPT: 'ppt',
  TEXT: 'text',
  ZIP: 'zip',
  PDF: 'pdf',
  FOLDER: 'folder',
  BT: 'bt', // BT种子文件
  APK: 'apk',
  UNKNOWN: 'unknown',
} as const;

export type FileType = typeof FileTypes[keyof typeof FileTypes];

// 文件类型到对应扩展名的映射
export const fileTypeExtensions: Record<FileType, string[]> = {
  [FileTypes.IMAGE]: ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico"],
  [FileTypes.VIDEO]: ["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm", "m4v", "3gp"],
  [FileTypes.AUDIO]: ["mp3", "wav", "ogg", "flac", "aac", "wma", "m4a", "ape", "ac3"],
  [FileTypes.WORD]: ["doc", "docx", "rtf"],
  [FileTypes.EXCEL]: ["xls", "xlsx", "csv"],
  [FileTypes.PPT]: ["ppt", "pptx"],
  [FileTypes.TEXT]: ["txt", "log", "md", "json", "xml", "html", "css", "js", "ts", "py", "java", "cpp", "c", "h"],
  [FileTypes.ZIP]: ["zip", "rar", "7z", "tar", "gz", "bz2", "xz"],
  [FileTypes.PDF]: ["pdf"],
  [FileTypes.BT]: ["torrent"],
  [FileTypes.APK]: ["apk","exe","ipa"],
  [FileTypes.FOLDER]: [],
  [FileTypes.UNKNOWN]: [],
};

// 文件类型到图标的映射
export const fileTypeIcons: Record<FileType, string> = {
  [FileTypes.IMAGE]: imageIcon,
  [FileTypes.VIDEO]: videoIcon,
  [FileTypes.AUDIO]: audioIcon,
  [FileTypes.WORD]: wordIcon,
  [FileTypes.EXCEL]: xlsIcon,
  [FileTypes.PPT]: pptIcon,
  [FileTypes.TEXT]: textIcon,
  [FileTypes.ZIP]: zipIcon,
  [FileTypes.PDF]: pdfIcon,
  [FileTypes.BT]: btIcon,
  [FileTypes.FOLDER]: folderIcon,
  [FileTypes.APK]: apkIcon,
  [FileTypes.UNKNOWN]: folderIcon,
};

// 构建扩展名到图标的映射表
export const getFileExtensionIconMap = () => {
  const map: Record<string, string> = {};
  
  Object.entries(fileTypeExtensions).forEach(([type, extensions]) => {
    const iconPath = fileTypeIcons[type as FileType];
    extensions.forEach(ext => {
      map[ext] = iconPath;
    });
  });
  
  return map;
};

// 根据文件名和是否为目录获取图标
export interface FileInfo {
  name: string;
  isDirectory?: boolean;
}

export const getFileIcon = (file: FileInfo): string => {
  // 如果是文件夹，直接返回文件夹图标
  if (file.isDirectory) {
    return fileTypeIcons[FileTypes.FOLDER];
  }

  // 获取文件扩展名
  const extension = file.name.split(".").pop()?.toLowerCase() || "";
  
  // 获取映射表
  const extensionMap = getFileExtensionIconMap();
  
  // 根据扩展名返回对应图标，如果没有匹配则返回默认图标
  return extensionMap[extension] || fileTypeIcons[FileTypes.UNKNOWN];
};

// 根据文件名判断文件类型
export const getFileType = (fileName: string, isDirectory?: boolean): FileType => {
  if (isDirectory) {
    return FileTypes.FOLDER;
  }

  const extension = fileName.split(".").pop()?.toLowerCase() || "";

  for (const [type, extensions] of Object.entries(fileTypeExtensions)) {
    if (extensions.includes(extension)) {
      return type as FileType;
    }
  }

  return FileTypes.UNKNOWN;
};

// ==================== TaskManager专用函数 ====================

/**
 * TaskManager专用：根据文件路径和源类型获取对应的图标
 * @param filePath 文件路径
 * @param srcType 源类型：'dir' 表示文件夹，其他值或undefined表示文件
 * @returns 图标路径
 */
export const getTaskManagerFileIcon = (filePath: string, srcType?: string): string => {
  if (!filePath) return folderIcon;

  // 如果是文件夹类型，返回文件夹图标
  if (srcType === 'dir') {
    return folderIcon;
  }

  // 获取文件扩展名
  const lastDotIndex = filePath.lastIndexOf('.');
  if (lastDotIndex === -1) return folderIcon; // 没有扩展名，使用默认图标

  const extension = filePath.substring(lastDotIndex + 1).toLowerCase();

  // 获取映射表
  const extensionMap = getFileExtensionIconMap();

  // 根据扩展名返回对应图标，如果没有匹配则返回其他文件图标
  return extensionMap[extension] || otherIcon;
};

/**
 * TaskManager专用：根据文件路径获取对应的图标（APP端使用）
 * @param filePath 文件路径
 * @returns 图标路径
 */
export const getTaskManagerFileIconForApp = (filePath: string): string => {
  if (!filePath) return folderIcon;

  // 获取文件扩展名
  const lastDotIndex = filePath.lastIndexOf('.');
  if (lastDotIndex === -1) return folderIcon; // 没有扩展名，使用默认图标

  const extension = filePath.substring(lastDotIndex + 1).toLowerCase();

  // 获取映射表
  const extensionMap = getFileExtensionIconMap();

  // 根据扩展名返回对应图标，如果没有匹配则返回其他文件图标
  return extensionMap[extension] || otherIcon;
};